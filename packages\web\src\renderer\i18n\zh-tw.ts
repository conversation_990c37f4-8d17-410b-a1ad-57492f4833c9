/*
|--------------------------------------------------------------------------
| 繁体中文语言包
|--------------------------------------------------------------------------
*/
export default {
  基于Vue和Electron的接口文档工具: '基于Vue和Electron的接口文档工具',
  项目列表: 'Project List',
  GitHub地址: 'GitHub地址',
  Gitee地址: 'Gitee地址',
  最近一次更新: '最近一次更新',
  api文档: 'api文檔',
  权限管理: '權限管理',
  刷新: '刷新',
  前进: '前進',
  后退: '後退',
  切换语言: '切換語言',
  更新进度: '更新進度',
  安装: '安裝',
  个人中心: '個人中心',
  检查更新: '檢查更新',
  版本: '版本',
  退出登录: '退出登錄',
  存在可用更新: '存在可用更新',
  没有可用更新: '沒有可用更新',
  暂无可用更新: '暫無可用更新',
  下载中: '下載中',
  下载完成: '下載完成',
  更新异常请稍后再试: '更新異常請稍後再試',
  账号登录: '賬號登錄',
  手机登录: '手機登錄',
  账号注册: '賬號註冊',
  忘记密码: '忘記密碼',
  请输入用户名: '請輸入用戶名',
  请输入密码: '請輸入密碼',
  '直接登录(体验账号，数据不会被保存)': '直接登錄(體驗賬號，數據不會被保存)',
  登录: '登錄',
  注册账号: '註冊賬號',
  '已有账号，忘记密码?': '已有賬號，忘記密碼?',
  跳转github: '跳轉github',
  跳转码云: '跳轉碼雲',
  跳转文档: '跳轉文檔',
  完整文档: '完整文檔',
  跳转部署文档: '跳轉部署文檔',
  客户端下载: '客户端下载',
  部署文档: '部署文檔',
  验证码: '驗證碼',
  请输入手机号: '請輸入手機號',
  请输入验证码: '請輸入驗證碼',
  请填写正确手机号: '請填寫正確手機號',
  请输入登录名称: '請輸入登錄名稱',
  请再次输入密码: '請再次輸入密碼',
  注册并登录: '註冊並登錄',
  请输入重置后密码: '請輸入重置後密碼',
  '只允许 数字  字符串 ! @ # 不允许其他字符串':
        '只允許 數字  字符串 ! @ # 不允許其他字符串',
  '数字+字符串，并且大于8位': '數字+字符串，並且大於8位',
  请完善必填信息: '請完善必填信息',
  '两次输入密码不一致!': '兩次輸入密碼不一致!',
  重置密码成功: '重置密碼成功',
  确定批量删除: '確定批量刪除',
  '个节点?': '個節點?',
  节点: '節點',
  提示: '提示',
  确定: '確定',
  取消: '取消',
  确定删除: '確定刪除',
  新增文件夹: '新增文件夾',
  新增文件: '新增文件',
  刷新banner: '刷新banner',
  项目分享: '在線鏈接',
  回收站: '回收站',
  导出文档: '導出文檔',
  导入文档: '導入文檔',
  操作审计: '歷史記錄',
  全局设置: '全局設置',
  '文档名称、文档url': '文檔名稱、文檔url',
  高级筛选: '高級篩選',
  操作人员: '操作人員',
  清空: '清空',
  录入日期: '錄入日期',
  今天: '今天',
  近两天: '近兩天',
  近三天: '近三天',
  近七天: '近七天',
  自定义: '自定義',
  至: '至',
  开始日期: '開始日期',
  结束日期: '結束日期',
  最近多少条: '最近多少條',
  '2条': '2條',
  '5条': '5條',
  '10条': '10條',
  '15条': '15條',
  更多操作: '更多操作',
  快捷操作: '快捷操作',
  点击发起连接建立WebSocket连接:
        '點擊工具欄按鈕新增文檔或者鼠標右鍵新增',
  不能为空: '不能為空',
  新建文档: '新建文檔',
  新建文件夹: '新建文件夾',
  剪切: '剪切',
  复制: '複製',
  生成副本: '生成副本',
  粘贴: '粘貼',
  重命名: '重命名',
  删除: '刪除',
  批量剪切: '批量剪切',
  批量复制: '批量複製',
  批量删除: '批量刪除',
  以模板新建: '以模板新建',
  单个文件夹里面文档个数不超过: '單個文件夾裡面文檔個數不超過',
  全局设置中可配置: '全局设置中可配置',
  个: '個',
  '域名、接口前缀、环境维护': '域名、服務器地址、環境維護',
  符合规范的接口前缀: '符合規範的服務器地址',
  'ip地址+路径(可选)': 'ip地址+路徑(可選)',
  例如: '例如',
  '域名+路径(可选)': '域名+路徑(可選)',
  前缀名称: '服務器名稱',
  '例如：张三本地': '例如：張三本地',
  接口前缀: '服務器地址',
  'ip+端口或域名': 'ip+端口或域名',
  请选择协议: '请选择协议',
  没有则不填: '没有则不填',
  不填则默认80: '不填则默认80',
  协议: '协议',
  端口: '端口',
  ip或域名: 'ip或域名',
  是否共享: '是否共享',
  仅自身可见: '僅本地',
  项目内成员可见: '项目内成员可见',
  确认添加: '確認添加',
  接口前缀必填: '服務器地址必填',
  操作: '操作',
  编辑: '編輯',
  确认: '確認',
  关闭: '關閉',
  接口前缀不符合规范: '服務器地址不符合規範',
  请输入前缀名称: '請輸入服務器名稱',
  限制可维护域名数不超过: '限制可維護域名數不超過',
  修改成功: '修改成功',
  '此操作将永久删除此条记录, 是否继续?':
        '此操作將永久刪除此條記錄, 是否繼續?',
  接口前缀不能为空: '服務器地址不能為空',
  '当前请求方法被禁止，可以在全局配置中进行相关配置':
        '當前請求方法被禁止，可以在全局配置中進行相關配置',
  Mock服务器: 'Mock服務器',
  环境维护: '環境維護',
  代理: '代理',
  '路径参数写法': '路径参数写法',
  '由于浏览器限制，非electron环境无法模拟发送请求':
        '由於瀏覽器限制，非electron環境無法模擬發送請求',
  发送请求: '發送請求',
  取消请求: '取消請求',
  保存接口: '保存接口',
  请求地址: '完整路徑',
  导入参数: '導入參數',
  格式化JSON: '格式化JSON',
  确认导入: '確認導入',
  无法解析该字符串: '無法解析該字符串',
  保存参数为模板: '保存參數為模板',
  模板名称: '模板名稱',
  保存: '保存',
  应用模板: '應用模板',
  过滤模板: '過濾模板',
  维护: '維護',
  暂无数据: '暫無數據',
  保存为模板: '保存為模板',
  'raw模块中json数据可用于快速调试，参数无法添加备注，如果需要添加备注可以选择在json模块中录入参数':
        'raw模塊中json數據可用於快速調試，參數無法添加備註，如果需要添加備註可以選擇在json模塊中錄入參數',
  点击隐藏: '點擊隱藏默認',
  个隐藏: '{msg}个隱藏',
  参数: '參數',
  名称: '名稱',
  修改名称: '修改名稱',
  状态码: '狀態碼',
  返回格式: '返回格式',
  新增: '新增',
  请求参数: '請求參數',
  Query参数: 'Query參數',
  Path参数: 'Path參數',
  'Body参数(application/json)': 'Body參數(application/json)',
  'Body参数(multipart/formdata)': 'Body參數(multipart/formdata)',
  'Body参数(x-www-form-urlencoded)': 'Body參數(x-www-form-urlencoded)',
  Body参数: 'Body參數',
  返回参数: '返回參數',
  请求头: '請求頭',
  预览: '預覽',
  布局: '布局',
  左右布局: '左右布局',
  上下布局: '上下布局',
  变量: '變量',
  联想值: '聯想值',
  备注信息: '備註信息',
  未实现的返回类型: '未實現的返回類型',
  变量维护: '變量維護',
  联想参数: '聯想參數',
  基本信息: '基本信息',
  请求方式: '請求方式',
  维护人员: '維護人員',
  创建人员: '創建人員',
  累计用时: '累計用時',
  更新日期: '更新日期',
  创建日期: '創建日期',
  下载文件: '下載文件',
  应用为响应值: '應用為響應值',
  应用为: '應用為',
  未命名: '未命名',
  总大小: '總大小',
  已传输: '已傳輸',
  进度: '進度',
  值: '值',
  时长: '時長',
  未请求数据: '未請求數據',
  大小: '大小',
  格式: '格式',
  返回值: '返回值',
  返回头: '返回頭',
  点击发送请求按钮发送请求: '點擊發送按鈕發送請求',
  '因浏览器限制，完整HTTP功能请下载Electron':
        '因瀏覽器限制，完整HTTP功能請下載Electron',
  '跨域、、请求头(user-agent,accept-encoding)等受限':
        '跨域、、請求頭(user-agent,accept-encoding)等受限',
  下载Electron: '下載Electron',
  导出类型: '導出類型',
  JSON文档: 'JSON文檔',
  导出到其他项目: '導出到其他項目',
  额外配置: '額外配置',
  选择导出: '選擇導出',
  开启后可以自由选择需要导出的文档: '開啟後可以自由選擇需要導出的文檔',
  总数: '總數',
  文件夹数量: '文件夾數量',
  文档数量: '文檔數量',
  确定导出: '確定導出',
  请至少选择一个文档导出: '請至少選擇一個文檔導出',
  将当前项目指定文档导出到其他项目: '將當前項目指定文檔導出到其他項目',
  '从左侧拖拽文档到右侧，右侧也可以进行简单的拖拽':
        '從左側拖拽文檔到右側，右側也可以進行簡單的拖拽',
  鼠标右键可以新增文件夹或者删除文件夹:
        '鼠標右鍵可以新增文件夾或者刪除文件夾',
  '暂无文档，请在项目中添加至少一个文档':
        '暫無文檔，請在項目中添加至少一個文檔',
  导入成功: '導入成功',
  当前版本: '當前版本',
  今日新增: '今日新增',
  接口总数: '接口總數',
  '支持：摸鱼文档、Swagger/OpenApi 3.0/Postman2.1':
        '支持：摸魚文檔、Swagger/OpenApi 3.0',
  '将文件拖到此处，或': '將文件拖到此處，或',
  点击上传: '點擊上傳',
  文档类型: '文檔類型',
  导入数据预览: '導入數據預覽',
  文档数: '文檔數',
  文件夹数: '文件夾數',
  文件夹命名方式: '文件夾命名方式',
  'none代表不存在文件夹，所有节点扁平放置':
        'none代表不存在文件夾，所有節點扁平放置',
  导入方式: '導入方式',
  请谨慎选择导入方式: '請謹慎選擇導入方式',
  追加方式: '追加方式',
  覆盖方式: '覆蓋方式',
  目标目录: '目標目錄',
  '选择需要挂载的节点，不选择则默认挂载到根目录':
        '選擇需要掛載的節點，不選擇則默認掛載到根目錄',
  确定导入: '確定導入',
  '未知的文件格式，无法解析': '未知的文件格式，無法解析',
  仅支持JSON格式或者YAML格式文件: '僅支持JSON格式或者YAML格式文件',
  文件大小不超过: '文件大小不超過',
  覆盖后的数据将无法还原: '覆蓋後的數據將無法還原',
  请选择需要导入的文件: '請選擇需要導入的文件',
  缺少Version信息: '缺少Version信息',
  缺少Info字段: '缺少Info字段',
  缺少servers字段: '缺少servers字段',
  servers字段必须为数组: 'servers字段必須為數組',
  'server对象中存在多个变量枚举值，但接口工具仅解析默认值':
        'server對象中存在多個變量枚舉值，但接口工具僅解析默認值',
  服务器: '服務器',
  缺少paths字段: '缺少paths字段',
  路径: '路徑',
  相关属性为空: '相關屬性為空',
  paths参数中存在方法: 'paths參數中存在方法',
  但是所匹配数据为空: '但是所匹配數據為空',
  链接名称: '鏈接名稱',
  生成链接: '生成鏈接',
  项目名称: '項目名稱',
  过期截至: '過期截至',
  链接: '鏈接',
  密码: '密碼',
  不需要密码: '不需要密碼',
  字段名: '字段名',
  参数字段名称: '參數字段名稱',
  类型: '類型',
  Path参数个数: 'Path參數個數',
  Query参数个数: 'Query參數個數',
  Body参数个数: 'Body參數個數',
  Response参数個數: 'Response參數個數',
  参数名称: '參數名稱',
  备注: '備註',
  参数值: '參數值',
  参数类型: '參數類型',
  是否删除当前参数: '是否刪除當前參數',
  确定批量删除当前选中节点: '確定批量刪除當前選中節點',
  过滤条件: '過濾條件',
  新增模板: '新增模板',
  '例如：默认返回值': '例如：默認返回值',
  '请求参数(Params)': '請求參數(Params)',
  '请求参数(Body)': '請求參數(Body)',
  确认新增: '確認新增',
  修改模板: '修改模板',
  请选择参数类型: '請選擇參數類型',
  创建者名称: '創建者名稱',
  参数模板: '參數模板',
  请输入模板名称: '請輸入模板名稱',
  请选择模板类型: '請選擇模板類型',
  新增变量: '新增變量',
  变量名称: '變量名稱',
  请输入变量名称: '請輸入變量名稱',
  变量值: '變量值',
  请输入变量值: '請輸入變量值',
  值类型: '值類型',
  变量列表: '變量列表',
  创建者: '創建者',
  '此操作将永久删除该域名, 是否继续?': '此操作將永久刪除該域名, 是否繼續?',
  删除成功: '刪除成功',
  新增文档: '新增文檔',
  文档名称: '文檔名稱',
  文件夹名称: '文件夾名稱',
  关闭右侧: '關閉右側',
  关闭左侧: '關閉左側',
  关闭其他: '關閉其他',
  全部关闭: '全部關閉',
  全部: '全部',
  强制全部关闭: '強制全部關閉',
  新增项目: '新增項目',
  选择成员: '選擇成員',
  请输入项目名称: '請輸入項目名稱',
  输入用户名或完整手机号查找用户: '輸入用戶名或真實姓名查找用戶',
  用户名: '用戶名',
  昵称: '真實姓名',
  '角色(权限)': '角色(權限)',
  只读: '只讀',
  仅查看项目: '僅查看項目',
  读写: '讀寫',
  新增和编辑文档: '新增和編輯文檔',
  管理员: '管理員',
  添加新成员: '添加新成員',
  请填写项目名称: '請填寫項目名稱',
  请勿重复添加: '請勿重複添加',
  修改项目: '修改項目',
  添加用户: '添加用戶',
  '确认删除当前成员吗?': '確認刪除當前成員嗎?',
  团队至少保留一个管理员: '團隊至少保留一個管理員',
  '确认离开当前团队吗?': '確認離開當前團隊嗎?',
  '确认改变当前管理员权限吗?': '確認改變當前管理員權限嗎?',
  成员管理: '成員管理',
  新建项目: '新建項目',
  导入项目: '導入項目',
  收藏的项目: '收藏的項目',
  收藏: '收藏',
  取消收藏: '取消收藏',
  最新更新: '最新更新',
  接口数: '接口數',
  全部项目: '全部項目',
  团队管理: '團隊管理',
  过期倒计时: '過期倒計時',
  确认密码: '確認密碼',
  无效的项目id: '無效的項目id',
  '当前接口不存在，可能已经被删除!': '當前接口不存在，可能已經被刪除!',
  关闭接口: '關閉接口',
  发送请求时候自动计算: '發送請求時候自動計算',
  消息的长度: '消息的長度',
  发送请求时候自动处理: '發送請求時候自動處理',
  用户代理软件信息: '用戶代理軟件信息',
  主机信息: '主機信息',
  客户端理解的编码方式: '客戶端理解的編碼方式',
  '当前的事务完成后，是否会关闭网络连接':
        '當前的事務完成後，是否會關閉網絡連接',
  根据body类型自动处理: '根據body類型自動處理',
  返回参数名称: '返回參數名稱',
  是否要保存对接口的修改: '是否要保存對接口的修改',
  '维护人员：': '維護人員：',
  '创建人员：': '創建人員：',
  '累计用时：': '累計用時：',
  '更新日期：': '更新日期：',
  '创建日期：': '創建日期：',
  '确定批量删除个节点?': '確定批量刪除個節點?',
  '确定删除节点?': '確定刪除節點?',
  未知请求类型: '未知請求類型',
  模板维护: '模板維護',
  新增前端路由: '新增前端路由',
  分组名称: '分組名稱',
  '名称&地址': '名稱&地址',
  新增路由: '新增路由',
  批量修改类型: '批量修改類型',
  路由名称: '路由名稱',
  路由地址: '路由地址',
  编辑菜单: '編輯菜單',
  菜单名称: '菜單名稱',
  菜单列表: '菜單列表',
  新增子菜单: '新增子菜單',
  支持鼠标右键新增和编辑菜单: '支持鼠標右鍵新增和編輯菜單',
  菜单可以进行拖拽排序: '菜單可以進行拖拽排序',
  参数值不能为null: '參數值不能為null',
  全选: '全選',
  修改角色: '修改角色',
  角色名称: '角色名稱',
  前端路由: '前端路由',
  后端路由: '後端路由',
  前端菜单: '前端菜單',
  创建时间: '創建時間',
  新增角色: '新增角色',
  修改服务端路由: '修改服務端路由',
  请求方法: '請求方法',
  批量修改服务端路由类型: '批量修改服務端路由類型',
  '新增用户': '新增用戶(默認密碼111111)',
  登录名称: '登錄名稱',
  手机号: '手機號',
  角色选择: '角色選擇',
  基础信息: '基礎信息',
  下载模板: '下載模板',
  导入用户: '導入用戶',
  上次登录: '上次登錄',
  登录次数: '登錄次數',
  角色信息: '角色信息',
  状态: '狀態',
  启用: '啟用',
  禁用: '禁用',
  确实要该用户吗: '確實要該用戶嗎',
  角色维护: '角色維護',
  菜单维护: '菜單維護',
  '后端路由(接口)': '後端路由(接口)',
  常用: '常用',
  '日期/时间': '日期/時間',
  图片: '圖片',
  中文文本: '中文文本',
  英文文本: '英文文本',
  地区相关: '地區相關',
  颜色: '顏色',
  中文名称: '中文名稱',
  中文单词: '中文單詞',
  中文句子: '中文句子',
  中文段落: '中文段落',
  中文标题: '中文標題',
  英文名称: '英文名稱',
  英文句子: '英文句子',
  英文单词: '英文單詞',
  英文标题: '英文標題',
  布尔值: '布爾值',
  '自然数(0,1,2,3,4)': '自然數(0,1,2,3,4)',
  '自然数(大于100)': '自然數(大於100)',
  '自然数(大于100小于200)': '自然數(大於100小於200)',
  '整数(-22,1,23)': '整數(-22,1,23)',
  '整数(大于100)': '整數(大於100)',
  '整数(大于100小于200)': '整數(大於100小於200)',
  浮点数: '浮點數',
  字符串: '字符串',
  英文段落: '英文段落',
  数字: '數字',
  '字符串(长度为5)': '字符串(長度為5)',
  '时间戳(精确到毫秒13位)': '時間戳(精確到毫秒13位)',
  日期时间: '日期時間',
  '日期(年月日)': '日期(年月日)',
  '时间(时分秒)': '時間(時分秒)',
  当前日期时间: '當前日期時間',
  '颜色(#ff6600)': '顏色(#ff6600)',
  '颜色(rgb(122,122,122))': '顏色(rgb(122,122,122))',
  '颜色rgb(122,122,122, 0.3)': '顏色rgb(122,122,122, 0.3)',
  '颜色hsl(222, 11, 31)': '顏色hsl(222, 11, 31)',
  '图片(150x100)': '圖片(150x100)',
  base64图片数据: 'base64圖片數據',
  base64图片数据100x100: 'base64圖片數據100x100',
  省: '省',
  市: '市',
  区: '區',
  此项不允许删除: '此項不允許刪除',
  删除当前行: '刪除當前行',
  传输数据类型为formData才能使用file类型:
        '傳輸數據類型為formData才能使用file類型',
  对象和数组不必填写参数值: '對象和數組不必填寫參數值',
  请选择: '請選擇',
  选择文件: '選擇文件',
  必有: '必有',
  参数描述与备注: '參數描述與備註',
  '参数不允许嵌套，例如：当请求方式为get时，请求参数只能为扁平数据':
        '參數不允許嵌套，例如：當請求方式為get時，請求參數只能為扁平數據',
  添加一条嵌套数据: '添加一條嵌套數據',
  根元素: '根元素',
  父元素为数组不必填写参数名称: '父元素為數組不必填寫參數名稱',
  输入参数名称自动换行: '輸入參數名稱',
  校验: '校驗',
  '参数类型不允许改变，eg：当请求方式为get时，请求参数类型只能为string':
        '參數類型不允許改變，eg：當請求方式為get時，請求參數類型只能為string',
  对象类型不必填写: '對象類型不必填寫',
  填写数字代表mock数据条数: '填寫數字代表mock數據條數',
  '参数值、@开头代表mock数据': '參數值、@開頭代表mock數據',
  '参数值、@代表mock，{0} 变量 {1}': '參數值、@代表mock，{0} 變量 {1}',
  '{0} fileValue {1}': '{0} fileValue {1}',
  可选: '可選',
  请输入标题: '請輸入標題',
  加载中: '加載中',
  请输入: '請輸入',
  双击还原: '雙擊還原',
  获取验证码: '獲取驗證碼',
  重新发送: '重新發送',
  重新获取: '重新獲取',
  序号: '序號',
  在左侧进行数据选择后方可删除数据: '在左側進行數據選擇後方可刪除數據',
  '此操作将删除条记录, 是否继续?': '此操作將刪除條記錄, 是否繼續?',
  请求信息: '请求信息',
  密码设置: '密碼設置',
  密码可不填写: '密碼可不填寫',
  请输入链接名称: '請輸入鏈接名稱',
  '请输入链接名称 eg:xxx团队': '請輸入鏈接名稱 eg:xxx團隊',
  过期时间: '過期時間',
  '不填默认一个月后过期，最大日期为一年': '不填默認一個月後過期，最大日期為一年',
  '1天后': '1天後',
  '1周后': '1週後',
  '1个月后': '1個月後',
  '1個季度後': '1個季度後',
  不过期: '不過期',
  选择分享: '選擇分享',
  '开启后可以自由选择需要分享的文档': '開啟後可以自由選擇需要分享的文檔',
  '请至少选择一个文档分享': '請至少選擇一個文檔分享',
  天后: '天後',
  文档分享: '文檔分享',
  分享链接: '分享鏈接',
  验证分享链接: '驗證分享鏈接',
  正在验证分享链接: '正在驗證分享鏈接',
  永久有效: '永久有效',
  文档内容: '文檔內容',
  这里是分享的API文档内容: '這裡是分享的API文檔內容',
  密码验证成功: '密碼驗證成功',
  密码错误: '密碼錯誤',
  密码验证失败: '密碼驗證失敗',
  请输入访问密码: '請輸入訪問密碼',
  分享链接无效缺少分享ID: '分享鏈接無效，缺少分享ID',
  获取分享信息失败请检查链接是否正确: '獲取分享信息失敗，請檢查鏈接是否正確',
  已过期: '已過期',
  天: '天',
  小时: '小時',
  分: '分',
  秒: '秒',
  认证中: '認證中...',
  暂无标签页: '暫無標籤頁',
  api_import_title: 'API文檔導入',
  api_import_desc: '導入並校驗你的Swagger或OpenAPI 3.1規範',
  // WebSocket相關翻譯
  自動發送間隔: '自動發送間隔',
  自動發送消息: '自動發送消息',
  自動發送: '自動發送',
  發送並清空: '發送並清空',
  自動發送內容: '自動發送內容',
  啟用自動發送: '啟用自動發送',
  自動發送間隔時間: '自動發送間隔時間',
  毫秒: '毫秒',
  自定義自動發送內容: '自定義自動發送內容',
  自動發送成功: '自動發送成功',
  自動發送失敗: '自動發送失敗',
  請輸入自動發送內容: '請輸入自動發送內容',
  自動發送間隔不能為空: '自動發送間隔不能為空',
  自動發送間隔必須大於0: '自動發送間隔必須大於0',
  自動發送已啟用: '自動發送已啟用',
  自動發送已停止: '自動發送已停止',
  发送间隔: '發送間隔',
  消息模板: '消息模板',
  选择模板: '選擇模板',
  暂无模板数据: '暫無模板數據',
  创建模板: '創建模板',
  创建消息模板: '創建消息模板',
  消息模板名称: '模板名稱',
  消息数据类型: '數據類型',
  消息数据值: '數據值',
  请输入消息模板名称: '請輸入模板名稱',
  请输入消息数据值: '請輸入數據值',
  消息模板名称不能为空: '模板名稱不能為空',
  确定要删除此消息模板吗: '確定要刪除此模板嗎？',
  消息模板删除成功: '模板刪除成功',
  消息模板创建成功: '模板創建成功',
  '模板名称长度在 1 到 50 个字符': '模板名稱長度在 1 到 50 個字符',
  请选择数据类型: '請選擇數據類型',
  模板名称已存在: '模板名稱已存在',
  模板创建失败: '模板創建失敗',
  模板删除失败: '模板刪除失敗',

  // 新增的未使用i18n的文本
  '本地': '本地',
  中文简体: '中文簡體',
  中文繁體: '中文繁體',
  主页面: '主頁面',
  最小化: '最小化',
  最大化: '最大化',
  取消最大化: '取消最大化',
  刷新主应用: '刷新主應用',
  切换变量选择模式支持变量或者直接选择文件: '切換變量選擇模式，支持變量或者直接選擇文件',
  '切换变量选择模式，支持变量或者直接选择文件': '切換變量選擇模式，支持變量或者直接選擇文件',
  变量模式: '變量模式',
  文件模式: '文件模式',
  不允许新增数据: '不允許新增数據',
  不允许删除数据: '不允許刪除数據',
  该请求头无法修改也无法取消发送: '該請求頭無法修改，也無法取消發送',
  '该请求头无法修改，也无法取消发送': '該請求頭無法修改，也無法取消發送',
  执行中: '執行中...',
  此操作将清空所有本地缓存是否继续: '此操作將清空所有本地緩存, 是否繼續?',
  已被删除: '已被刪除',
  连接中: '連接中',
  已连接: '已連接',
  连接失败: '連接失敗',
  已断开: '已斷開',
  发送: '發送',
  连接: '連接',
  断开连接: '斷開連接',
  消息历史: '消息歷史',
  清空历史: '清空歷史',
  发送消息: '發送消息',
  请输入消息内容: '請輸入消息內容',
  连接地址: '連接地址',
  请输入WebSocket连接地址: '請輸入WebSocket連接地址',
  连接参数: '連接參數',
  请求头参数: '請求頭參數',
  WebSocket连接测试: 'WebSocket連接測試',
  点击连接按钮建立WebSocket连接: '點擊連接按鈕建立WebSocket連接',
  消息内容: '消息內容',
  项目特色功能视频演示: '項目特色功能視頻演示',
  下载: '下載',
  个人基本信息: '個人基本資訊',
  用户头像: '用戶頭像',
  更换头像: '更換頭像',
  所属团队: '所屬團隊',
  编辑信息: '編輯資訊',
  返回上级: '返回上級',
  修改密码: '修改密碼',
  原密码: '原密碼',
  请输入原密码: '請輸入原密碼',
  新密码: '新密碼',
  请输入新密码: '請輸入新密碼',
  请再次输入新密码: '請再次輸入新密碼',
  支持正则表达式: '支持正則表達式',
  输入关键词筛选: '輸入關鍵詞篩選',
  切换正则表达式模式: '切換正則表達式模式',
  切换原始数据视图: '切換原始數據視圖',
  下载SSE数据: '下載SSE數據',
  找到: '找到',
  条匹配结果: '條匹配結果',
  未找到匹配结果: '未找到匹配結果',
  下载WebSocket数据: '下載WebSocket數據',
  接收: '接收',
  开始连接: '開始連接',
  重连中: '重連中',
  正则表达式错误: '正則表達式錯誤',
  未知错误: '未知錯誤',
  下载失败: '下載失敗',
  重试第: '重試第',
  次URL: '次，URL:',
  消息详情: '消息詳情',
  错误信息: '錯誤資訊',
  内容类型: '內容類型',
  重连次数: '重連次數',
  下次重试: '下次重試',
  断开原因: '斷開原因',
  手动断开: '手動斷開',
  自动断开: '自動斷開',
  全部消息: '全部消息',
};
