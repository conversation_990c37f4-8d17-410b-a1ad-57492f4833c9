/*
|--------------------------------------------------------------------------
| 日本語言語パック
|--------------------------------------------------------------------------
*/
export default {
  基于Vue和Electron的接口文档工具: 'VueとElectronベースのAPIドキュメントツール',
  项目列表: 'プロジェクト一覧',
  GitHub地址: 'GitHubアドレス',
  Gitee地址: 'Giteeアドレス',
  最近一次更新: '最終更新',
  api文档: 'APIドキュメント',
  权限管理: '権限管理',
  刷新: '更新',
  前进: '進む',
  后退: '戻る',
  切换语言: '言語切替',
  更新进度: '更新進度',
  安装: 'インストール',
  个人中心: '個人センター',
  检查更新: '更新確認',
  版本: 'バージョン',
  退出登录: 'ログアウト',
  存在可用更新: '利用可能な更新があります',
  没有可用更新: '利用可能な更新はありません',
  暂无可用更新: '利用可能な更新はありません',
  下载中: 'ダウンロード中',
  下载完成: 'ダウンロード完了',
  更新异常请稍后再试: '更新エラー、後でもう一度お試しください',
  账号登录: 'アカウントログイン',
  手机登录: '電話ログイン',
  账号注册: 'アカウント登録',
  忘记密码: 'パスワードを忘れた',
  请输入用户名: 'ユーザー名を入力してください',
  请输入密码: 'パスワードを入力してください',
  '直接登录(体验账号，数据不会被保存)': '直接ログイン（体験アカウント、データは保存されません）',
  登录: 'ログイン',
  注册账号: 'アカウント登録',
  '已有账号，忘记密码?': 'アカウントをお持ちですか？パスワードを忘れましたか？',
  跳转github: 'GitHubへ移動',
  跳转码云: 'Giteeへ移動',
  跳转文档: 'ドキュメントへ移動',
  完整文档: '完全なドキュメント',
  跳转部署文档: 'デプロイドキュメントへ移動',
  部署文档: 'デプロイドキュメント',
  客户端下载: 'クライアントダウンロード',
  验证码: '認証コード',
  请输入手机号: '電話番号を入力してください',
  请输入验证码: '認証コードを入力してください',
  请填写正确手机号: '正しい電話番号を入力してください',
  请输入登录名称: 'ログイン名を入力してください',
  请再次输入密码: 'パスワードを再入力してください',
  注册并登录: '登録してログイン',
  请输入重置后密码: '新しいパスワードを入力してください',
  '只允许 数字  字符串 ! @ # 不允许其他字符串': '数字、文字列、! @ # のみ許可、その他の文字は不可',
  '数字+字符串，并且大于8位': '数字+文字列、8文字以上',
  请完善必填信息: '必須情報を入力してください',
  '两次输入密码不一致!': 'パスワードが一致しません！',
  重置密码成功: 'パスワードリセット成功',
  确定批量删除: '一括削除確認',
  '个节点?': '個のノード？',
  节点: 'ノード',
  提示: 'ヒント',
  确定: '確認',
  取消: 'キャンセル',
  确定删除: '削除確認',
  新增文件夹: '新しいフォルダ',
  新增文件: '新しいファイル',
  刷新banner: 'バナー更新',
  项目分享: 'プロジェクト共有',
  回收站: 'ごみ箱',
  导出文档: 'ドキュメントエクスポート',
  导入文档: 'ドキュメントインポート',
  操作审计: '操作監査',
  全局设置: 'グローバル設定',
  '文档名称、文档url': 'ドキュメント名、ドキュメントURL',
  高级筛选: '高度なフィルタ',
  操作人员: 'オペレーター',
  清空: 'クリア',
  录入日期: '入力日付',
  今天: '今日',
  近两天: '過去2日',
  近三天: '過去3日',
  近七天: '過去7日',
  自定义: 'カスタム',
  至: 'から',
  开始日期: '開始日',
  结束日期: '終了日',
  最近多少条: '最近の記録数',
  '2条': '2件',
  '5条': '5件',
  '10条': '10件',
  '15条': '15件',
  更多操作: 'その他の操作',
  快捷操作: 'クイック操作',
  点击发起连接建立WebSocket连接: '接続ボタンをクリックしてWebSocket接続を確立',
  不能为空: '空にできません',
  新建文档: '新しいドキュメント',
  新建文件夹: '新しいフォルダ',
  剪切: '切り取り',
  复制: 'コピー',
  生成副本: 'コピーを生成',
  粘贴: '貼り付け',
  重命名: '名前変更',
  删除: '削除',
  批量剪切: '一括切り取り',
  批量复制: '一括コピー',
  批量删除: '一括削除',
  以模板新建: 'テンプレートから新規作成',
  单个文件夹里面文档个数不超过: '単一フォルダ内のドキュメント数は以下を超えることはできません',
  全局设置中可配置: 'グローバル設定で設定可能',
  个: '個',
  '域名、接口前缀、环境维护': 'ドメイン、APIプレフィックス、環境メンテナンス',
  符合规范的接口前缀: '標準に準拠したAPIプレフィックス',
  'ip地址+路径(可选)': 'IPアドレス+パス（オプション）',
  例如: '例',
  '域名+路径(可选)': 'ドメイン+パス（オプション）',
  前缀名称: 'プレフィックス名',
  '例如：张三本地': '例：張三ローカル',
  'ip+端口或域名': 'IP+ポートまたはドメイン',
  请选择协议: 'プロトコルを選択してください',
  没有则不填: 'なければ空白',
  不填则默认80: '空白の場合デフォルト80',
  端口: 'ポート',
  ip或域名: 'IPまたはドメイン',
  协议: 'プロトコル',
  接口前缀: 'APIプレフィックス',
  是否共享: '共有するかどうか',
  仅自身可见: '自分のみ表示',
  项目内成员可见: 'プロジェクトメンバーに表示',
  确认添加: '追加確認',
  接口前缀必填: 'APIプレフィックスは必須',
  操作: '操作',
  编辑: '編集',
  确认: '確認',
  关闭: '閉じる',
  接口前缀不符合规范: 'APIプレフィックスが標準に準拠していません',
  请输入前缀名称: 'プレフィックス名を入力してください',
  限制可维护域名数不超过: 'メンテナンス可能なドメイン数の制限',
  修改成功: '変更成功',
  '此操作将永久删除此条记录, 是否继续?': 'この操作はこのレコードを永久に削除します。続行しますか？',
  接口前缀不能为空: 'APIプレフィックスは空にできません',
  '当前请求方法被禁止，可以在全局配置中进行相关配置': '現在のリクエストメソッドは禁止されています。グローバル設定で関連設定を行えます',
  Mock服务器: 'Mockサーバー',
  环境维护: '環境メンテナンス',
  代理: 'プロキシ',
  '路径参数写法': 'パスパラメータの書き方',
  '由于浏览器限制，非electron环境无法模拟发送请求': 'ブラウザの制限により、非electron環境ではリクエスト送信をシミュレートできません',
  发送请求: 'リクエスト送信',
  取消请求: 'リクエストキャンセル',
  保存接口: 'インターフェース保存',
  导入参数: 'パラメータインポート',
  格式化JSON: 'JSON整形',
  确认导入: 'インポート確認',
  无法解析该字符串: 'この文字列を解析できません',
  保存参数为模板: 'パラメータをテンプレートとして保存',
  模板名称: 'テンプレート名',
  保存: '保存',
  应用模板: 'テンプレート適用',
  过滤模板: 'テンプレートフィルタ',
  维护: 'メンテナンス',
  暂无数据: 'データなし',
  保存为模板: 'テンプレートとして保存',
  'raw模块中json数据可用于快速调试，参数无法添加备注，如果需要添加备注可以选择在json模块中录入参数': 'rawモジュールのJSONデータは高速デバッグに使用できます。パラメータに備考を追加できません。備考を追加する場合は、JSONモジュールでパラメータを入力してください',
  点击隐藏: 'クリックして非表示',
  个隐藏: '個非表示',
  原始值: '元の値',
  参数: 'パラメータ',
  名称: '名前',
  修改名称: '名前変更',
  状态码: 'ステータスコード',
  返回格式: '戻り形式',
  新增: '追加',
  请求参数: 'リクエストパラメータ',
  Query参数: 'Queryパラメータ',
  Path参数: 'Pathパラメータ',
  'Body参数(application/json)': 'Bodyパラメータ(application/json)',
  'Body参数(multipart/formdata)': 'Bodyパラメータ(multipart/formdata)',
  'Body参数(x-www-form-urlencoded)': 'Bodyパラメータ(x-www-form-urlencoded)',
  Body参数: 'Bodyパラメータ',
  返回参数: '戻りパラメータ',
  请求头: 'リクエストヘッダー',
  预览: 'プレビュー',
  布局: 'レイアウト',
  左右布局: '左右レイアウト',
  上下布局: '上下レイアウト',
  变量: '変数',
  联想值: '連想値',
  备注信息: '備考情報',
  未实现的返回类型: '未実装の戻り型',
  变量维护: '変数メンテナンス',
  联想参数: '連想パラメータ',
  基本信息: '基本情報',
  请求地址: 'リクエストURL',
  请求方式: 'リクエストメソッド',
  维护人员: 'メンテナンス担当者',
  创建人员: '作成者',
  累计用时: '累計時間',
  更新日期: '更新日',
  创建日期: '作成日',
  下载文件: 'ファイルダウンロード',
  应用为响应值: 'レスポンス値として適用',
  应用为: '適用先',
  未命名: '無名',
  总大小: '総サイズ',
  已传输: '転送済み',
  进度: '進度',
  值: '値',
  时长: '時間',
  未请求数据: 'リクエストデータなし',
  大小: 'サイズ',
  格式: '形式',
  返回值: '戻り値',
  返回头: '戻りヘッダー',
  点击发送请求按钮发送请求: 'リクエスト送信ボタンをクリックしてリクエストを送信',
  '因浏览器限制，完整HTTP功能请下载Electron': 'ブラウザの制限により、完全なHTTP機能についてはElectronをダウンロードしてください',
  '跨域、、请求头(user-agent,accept-encoding)等受限': 'CORS、リクエストヘッダー(user-agent,accept-encoding)などが制限されています',
  下载Electron: 'Electronダウンロード',
  导出类型: 'エクスポートタイプ',
  JSON文档: 'JSONドキュメント',
  导出到其他项目: '他のプロジェクトにエクスポート',
  额外配置: '追加設定',
  选择导出: 'エクスポート選択',
  开启后可以自由选择需要导出的文档: '有効にすると、エクスポートするドキュメントを自由に選択できます',
  总数: '総数',
  文件夹数量: 'フォルダ数',
  文档数量: 'ドキュメント数',
  确定导出: 'エクスポート確認',
  请至少选择一个文档导出: '少なくとも1つのドキュメントを選択してエクスポートしてください',
  将当前项目指定文档导出到其他项目: '現在のプロジェクトの指定ドキュメントを他のプロジェクトにエクスポート',
  '从左侧拖拽文档到右侧，右侧也可以进行简单的拖拽': '左側からドキュメントを右側にドラッグ、右側でも簡単なドラッグが可能',
  鼠标右键可以新增文件夹或者删除文件夹: '右クリックでフォルダの追加または削除が可能',
  '暂无文档，请在项目中添加至少一个文档': 'ドキュメントがありません。プロジェクトに少なくとも1つのドキュメントを追加してください',
  导入成功: 'インポート成功',
  当前版本: '現在のバージョン',
  今日新增: '今日の新規',
  接口总数: 'インターフェース総数',
  '支持：摸鱼文档、Swagger/OpenApi 3.0/Postman2.1': 'サポート：摸鱼文档、Swagger/OpenApi 3.0/Postman2.1',
  '将文件拖到此处，或': 'ファイルをここにドラッグするか、',
  点击上传: 'クリックしてアップロード',
  文档类型: 'ドキュメントタイプ',
  导入数据预览: 'インポートデータプレビュー',
  文档数: 'ドキュメント数',
  文件夹数: 'フォルダ数',
  文件夹命名方式: 'フォルダ命名方式',
  'none代表不存在文件夹，所有节点扁平放置': 'noneはフォルダが存在しないことを表し、すべてのノードがフラットに配置されます',
  导入方式: 'インポート方式',
  请谨慎选择导入方式: 'インポート方式を慎重に選択してください',
  追加方式: '追加方式',
  覆盖方式: '上書き方式',
  目标目录: 'ターゲットディレクトリ',
  '选择需要挂载的节点，不选择则默认挂载到根目录': 'マウントするノードを選択、選択しない場合はルートディレクトリにデフォルトでマウント',
  确定导入: 'インポート確認',
  '未知的文件格式，无法解析': '不明なファイル形式、解析できません',
  仅支持JSON格式或者YAML格式文件: 'JSON形式またはYAML形式のファイルのみサポート',
  文件大小不超过: 'ファイルサイズは以下を超えることはできません',
  覆盖后的数据将无法还原: '上書き後のデータは復元できません',
  请选择需要导入的文件: 'インポートするファイルを選択してください',
  缺少Version信息: 'Version情報が不足',
  缺少Info字段: 'Infoフィールドが不足',
  缺少servers字段: 'serversフィールドが不足',
  servers字段必须为数组: 'serversフィールドは配列である必要があります',
  'server对象中存在多个变量枚举值，但接口工具仅解析默认值': 'serverオブジェクトに複数の変数列挙値が存在しますが、インターフェースツールはデフォルト値のみを解析します',
  服务器: 'サーバー',
  缺少paths字段: 'pathsフィールドが不足',
  路径: 'パス',
  相关属性为空: '関連属性が空',
  paths参数中存在方法: 'pathsパラメータにメソッドが存在',
  但是所匹配数据为空: 'しかし、マッチするデータが空',
  链接名称: 'リンク名',
  生成链接: 'リンク生成',
  项目名称: 'プロジェクト名',
  过期截至: '有効期限',
  链接: 'リンク',
  密码: 'パスワード',
  不需要密码: 'パスワード不要',
  字段名: 'フィールド名',
  参数字段名称: 'パラメータフィールド名',
  类型: 'タイプ',
  Path参数个数: 'Pathパラメータ数',
  Query参数个数: 'Queryパラメータ数',
  Body参数个数: 'Bodyパラメータ数',
  Response参数个数: 'Responseパラメータ数',
  参数名称: 'パラメータ名',
  备注: '備考',
  参数值: 'パラメータ値',
  参数类型: 'パラメータタイプ',
  是否删除当前参数: '現在のパラメータを削除しますか',
  确定批量删除当前选中节点: '現在選択されているノードの一括削除を確認',
  过滤条件: 'フィルタ条件',
  新增模板: 'テンプレート追加',
  '例如：默认返回值': '例：デフォルト戻り値',
  '请求参数(Params)': 'リクエストパラメータ(Params)',
  '请求参数(Body)': 'リクエストパラメータ(Body)',
  确认新增: '追加確認',
  修改模板: 'テンプレート変更',
  请选择参数类型: 'パラメータタイプを選択してください',
  创建者名称: '作成者名',
  参数模板: 'パラメータテンプレート',
  请输入模板名称: 'テンプレート名を入力してください',
  请选择模板类型: 'テンプレートタイプを選択してください',
  新增变量: '変数追加',
  变量名称: '変数名',
  请输入变量名称: '変数名を入力してください',
  变量值: '変数値',
  请输入变量值: '変数値を入力してください',
  值类型: '値タイプ',
  变量列表: '変数リスト',
  创建者: '作成者',
  '此操作将永久删除该域名, 是否继续?': 'この操作はこのドメインを永久に削除します。続行しますか？',
  '此操作将永久删除该变量, 是否继续?': 'この操作はこの変数を永久に削除します。続行しますか？',
  删除成功: '削除成功',
  新增文档: 'ドキュメント追加',
  文档名称: 'ドキュメント名',
  文件夹名称: 'フォルダ名',
  关闭右侧: '右側を閉じる',
  关闭左侧: '左側を閉じる',
  关闭其他: 'その他を閉じる',
  全部关闭: 'すべて閉じる',
  全部: 'すべて',
  强制全部关闭: '強制的にすべて閉じる',
  新增项目: 'プロジェクト追加',
  选择成员: 'メンバー選択',
  请输入项目名称: 'プロジェクト名を入力してください',
  输入用户名或完整手机号查找用户: 'ユーザー名または完全な電話番号を入力してユーザーを検索',
  用户名: 'ユーザー名',
  昵称: 'ニックネーム',
  '角色(权限)': '役割（権限）',
  只读: '読み取り専用',
  仅查看项目: 'プロジェクト表示のみ',
  读写: '読み書き',
  新增和编辑文档: 'ドキュメントの追加と編集',
  管理员: '管理者',
  添加新成员: '新しいメンバーを追加',
  请填写项目名称: 'プロジェクト名を入力してください',
  请勿重复添加: '重複して追加しないでください',
  '用户已存在、请勿重复添加': 'ユーザーが既に存在します。重複して追加しないでください',
  修改项目: 'プロジェクト変更',
  添加用户: 'ユーザー追加',
  '确认删除当前成员吗?': '現在のメンバーを削除しますか？',
  团队至少保留一个管理员: 'チームには少なくとも1人の管理者を残してください',
  '确认离开当前团队吗?': '現在のチームを離れますか？',
  '确认改变当前管理员权限吗?': '現在の管理者権限を変更しますか？',
  成员管理: 'メンバー管理',
  新建项目: '新しいプロジェクト',
  导入项目: 'プロジェクトインポート',
  收藏的项目: 'お気に入りプロジェクト',
  收藏: 'お気に入り',
  取消收藏: 'お気に入り解除',
  最新更新: '最新更新',
  接口数: 'インターフェース数',
  全部项目: 'すべてのプロジェクト',
  团队管理: 'チーム管理',
  过期倒计时: '有効期限カウントダウン',
  确认密码: 'パスワード確認',
  无效的项目id: '無効なプロジェクトID',
  '当前接口不存在，可能已经被删除!': '現在のインターフェースが存在しません。削除された可能性があります！',
  关闭接口: 'インターフェースを閉じる',
  发送请求时候自动计算: 'リクエスト送信時に自動計算',
  消息的长度: 'メッセージの長さ',
  发送请求时候自动处理: 'リクエスト送信時に自動処理',
  用户代理软件信息: 'ユーザーエージェントソフトウェア情報',
  主机信息: 'ホスト情報',
  客户端理解的编码方式: 'クライアントが理解するエンコーディング方式',
  '当前的事务完成后，是否会关闭网络连接': '現在のトランザクション完了後、ネットワーク接続を閉じるかどうか',
  根据body类型自动处理: 'bodyタイプに基づいて自動処理',
  返回参数名称: '戻りパラメータ名',
  是否要保存对接口的修改: 'インターフェースへの変更を保存しますか',
  '维护人员：': 'メンテナンス担当者：',
  '创建人员：': '作成者：',
  '累计用时：': '累計時間：',
  '更新日期：': '更新日：',
  '创建日期：': '作成日：',
  '确定批量删除个节点?': '個のノードを一括削除しますか？',
  '确定删除节点?': 'ノードを削除しますか？',
  未知请求类型: '不明なリクエストタイプ',
  模板维护: 'テンプレートメンテナンス',
  新增前端路由: 'フロントエンドルート追加',
  分组名称: 'グループ名',
  '名称&地址': '名前とアドレス',
  新增路由: 'ルート追加',
  批量修改类型: '一括タイプ変更',
  路由名称: 'ルート名',
  路由地址: 'ルートアドレス',
  编辑菜单: 'メニュー編集',
  菜单名称: 'メニュー名',
  菜单列表: 'メニューリスト',
  新增子菜单: 'サブメニュー追加',
  支持鼠标右键新增和编辑菜单: '右クリックでメニューの追加と編集をサポート',
  菜单可以进行拖拽排序: 'メニューはドラッグで並び替え可能',
  参数值不能为null: 'パラメータ値はnullにできません',
  全选: 'すべて選択',
  修改角色: '役割変更',
  角色名称: '役割名',
  前端路由: 'フロントエンドルート',
  后端路由: 'バックエンドルート',
  前端菜单: 'フロントエンドメニュー',
  创建时间: '作成時間',
  新增角色: '役割追加',
  修改服务端路由: 'サーバールート変更',
  请求方法: 'リクエストメソッド',
  批量修改服务端路由类型: 'サーバールートタイプの一括変更',
  '新增用户': 'ユーザー追加',
  登录名称: 'ログイン名',
  手机号: '電話番号',
  角色选择: '役割選択',
  修改: '変更',
  基础信息: '基本情報',
  下载模板: 'テンプレートダウンロード',
  导入用户: 'ユーザーインポート',
  上次登录: '最終ログイン',
  登录次数: 'ログイン回数',
  角色信息: '役割情報',
  状态: 'ステータス',
  启用: '有効',
  禁用: '無効',
  确实要该用户吗: 'このユーザーをしますか',
  角色维护: '役割メンテナンス',
  菜单维护: 'メニューメンテナンス',
  '后端路由(接口)': 'バックエンドルート（インターフェース）',
  // WebSocket関連翻訳
  自動送信間隔: '自動送信間隔',
  自動送信メッセージ: '自動送信メッセージ',
  自動送信: '自動送信',
  送信してクリア: '送信してクリア',
  自動送信内容: '自動送信内容',
  自動送信を有効にする: '自動送信を有効にする',
  自動送信間隔時間: '自動送信間隔時間',
  ミリ秒: 'ミリ秒',
  カスタム自動送信内容: 'カスタム自動送信内容',
  自動送信成功: '自動送信成功',
  自動送信失敗: '自動送信失敗',
  自動送信内容を入力してください: '自動送信内容を入力してください',
  自動送信間隔は空にできません: '自動送信間隔は空にできません',
  自動送信間隔は0より大きくなければなりません: '自動送信間隔は0より大きくなければなりません',
  自動发送已启用: '自動送信が有効になりました',
  自動发送已停止: '自動送信が停止しました',
  发送间隔: '送信間隔',
  
  // 新たな未翻訳テキスト
  '本地': 'ローカル',
  中文简体: '中国語簡体',
  中文繁體: '中国語繁体',
  主页面: 'ホーム',
  最小化: '最小化',
  最大化: '最大化',
  取消最大化: '最大化解除',
  刷新主应用: 'メインアプリ更新',
  切换变量选择模式支持变量或者直接选择文件: '変数選択モードを切り替え、変数または直接ファイル選択をサポート',
  变量模式: '変数モード',
  文件模式: 'ファイルモード',
  不允许新增数据: 'データの追加は許可されていません',
  不允许删除数据: 'データの削除は許可されていません',
  该请求头无法修改也无法取消发送: 'このリクエストヘッダーは変更またはキャンセルできません',
  执行中: '実行中...',
  此操作将清空所有本地缓存是否继续: 'この操作はすべてのローカルキャッシュをクリアします。続行しますか？',
  已被删除: '削除済み',
  连接中: '接続中',
  已连接: '接続済み',
  连接失败: '接続失敗',
  已断开: '切断済み',
  发送: '送信',
  连接: '接続',
  断开连接: '接続を切断',
  消息历史: 'メッセージ履歴',
  清空历史: '履歴をクリア',
  发送消息: 'メッセージ送信',
  请输入消息内容: 'メッセージ内容を入力してください',
  连接地址: '接続URL',
  请输入WebSocket连接地址: 'WebSocket接続URLを入力してください',
  连接参数: '接続パラメータ',
  请求头参数: 'リクエストヘッダーパラメータ',
  WebSocket连接测试: 'WebSocket接続テスト',
  点击连接按钮建立WebSocket连接: '接続ボタンをクリックしてWebSocket接続を確立',
  消息内容: 'メッセージ内容',
  项目特色功能视频演示: 'プロジェクト特徴機能ビデオデモ',
  下载: 'ダウンロード',
  个人基本信息: '個人基本情報',
  用户头像: 'ユーザーアバター',
  更换头像: 'アバター変更',
  所属团队: '所属チーム',
  编辑信息: '情報編集',
  返回上级: '上位に戻る',
  修改密码: 'パスワード変更',
  原密码: '元のパスワード',
  请输入原密码: '元のパスワードを入力してください',
  新密码: '新しいパスワード',
  请输入新密码: '新しいパスワードを入力してください',
  请再次输入新密码: '新しいパスワードを再度入力してください',
  支持正则表达式: '正規表現をサポート',
  输入关键词筛选: 'キーワードでフィルター',
  切换正则表达式模式: '正規表現モードの切り替え',
  切换原始数据视图: '生データビューの切り替え',
  下载SSE数据: 'SSEデータダウンロード',
  找到: '見つかりました',
  条匹配结果: '件の一致結果',
  未找到匹配结果: '一致する結果が見つかりません',
  下载WebSocket数据: 'WebSocketデータダウンロード',
  接收: '受信',
  开始连接: '接続開始',
  重连中: '再接続中',
  正则表达式错误: '正規表現エラー',
  未知错误: '不明なエラー',
  下载失败: 'ダウンロード失敗',
  重试第: '再試行',
  次URL: '回目、URL:',
  消息详情: 'メッセージ詳細',
  错误信息: 'エラー情報',
  内容类型: 'コンテンツタイプ',
  重连次数: '再接続回数',
  下次重试: '次回再試行',
  断开原因: '切断理由',
  手动断开: '手動切断',
  自动断开: '自動切断',
  全部消息: '全てのメッセージ',
  自動发送: '自動送信',
}